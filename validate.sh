#!/bin/bash

# =============================================================================
# Terraform Configuration Validation Script
# =============================================================================

echo "🔍 Validating Terraform Configuration..."
echo "========================================"

# Check if terraform is installed
if ! command -v terraform &> /dev/null; then
    echo "❌ Terraform is not installed or not in PATH"
    echo "Please install Terraform from: https://www.terraform.io/downloads.html"
    exit 1
fi

# Check if configuration files exist
echo "📁 Checking configuration files..."
if [ ! -f "oss_all.tf" ]; then
    echo "❌ oss_all.tf not found"
    exit 1
fi

if [ ! -f "terraform.tfvars.example" ]; then
    echo "❌ terraform.tfvars.example not found"
    exit 1
fi

echo "✅ Configuration files found"

# Initialize terraform (if not already done)
echo "🚀 Initializing Terraform..."
if [ ! -d ".terraform" ]; then
    terraform init
    if [ $? -ne 0 ]; then
        echo "❌ Terraform initialization failed"
        exit 1
    fi
else
    echo "✅ Terraform already initialized"
fi

# Validate configuration
echo "🔧 Validating Terraform configuration..."
terraform validate
if [ $? -eq 0 ]; then
    echo "✅ Terraform configuration is valid"
else
    echo "❌ Terraform configuration validation failed"
    exit 1
fi

# Format check
echo "📝 Checking Terraform formatting..."
terraform fmt -check
if [ $? -eq 0 ]; then
    echo "✅ Terraform formatting is correct"
else
    echo "⚠️  Terraform formatting needs adjustment. Run 'terraform fmt' to fix."
fi

# Check if terraform.tfvars exists
echo "⚙️  Checking configuration..."
if [ ! -f "terraform.tfvars" ]; then
    echo "⚠️  terraform.tfvars not found. Please copy from terraform.tfvars.example and customize."
    echo "   cp terraform.tfvars.example terraform.tfvars"
else
    echo "✅ terraform.tfvars found"
    
    # Plan (dry run)
    echo "📋 Running Terraform plan (dry run)..."
    terraform plan -out=tfplan
    if [ $? -eq 0 ]; then
        echo "✅ Terraform plan successful"
        rm -f tfplan
    else
        echo "❌ Terraform plan failed. Please check your configuration."
        exit 1
    fi
fi

echo ""
echo "🎉 Validation completed successfully!"
echo ""
echo "Next steps:"
echo "1. Copy terraform.tfvars.example to terraform.tfvars"
echo "2. Customize the variables in terraform.tfvars"
echo "3. Run 'terraform plan' to review changes"
echo "4. Run 'terraform apply' to create resources"
