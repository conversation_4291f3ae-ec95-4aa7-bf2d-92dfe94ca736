# JobXJoy Extension Terraform - OSS + CDN Multi-Domain Configuration

这个 Terraform 配置可以根据 `domain_list` 变量循环创建多个 OSS 存储桶、CDN 域名和相关配置。

## 功能特性

- 🚀 **批量创建**: 根据配置列表自动创建多个域名的完整基础设施
- 🎨 **图片处理**: 为每个存储桶配置自定义的图片处理样式
- 🔒 **CDN 认证**: 自动配置 CDN 防盗链认证
- 🌐 **DNS 解析**: 自动创建 DNS CNAME 记录
- 📊 **性能优化**: 配置 CDN 缓存和回源优化
- 🏷️ **资源标签**: 为所有资源添加统一的标签管理

## 架构说明

每个域名配置将创建以下资源：

```
domain_list[key] →
├── OSS Bucket (存储桶)
├── OSS Bucket Styles (图片处理样式)
├── CDN Domain (CDN 加速域名)
├── CDN Configs (CDN 配置)
│   ├── Authentication (防盗链认证)
│   ├── Performance (性能优化)
│   └── Origin (回源设置)
└── DNS Record (域名解析)
```

## 快速开始

### 1. 准备配置文件

```bash
# 复制示例配置文件
cp terraform.tfvars.example terraform.tfvars

# 编辑配置文件
vim terraform.tfvars
```

### 2. 修改配置

在 `terraform.tfvars` 中修改以下关键配置：

```hcl
# 基础配置
domain_name = "your-domain.com"  # 替换为您的域名
resource_group_id = "your-rg-id"  # 替换为您的资源组ID

# 域名列表配置
domain_list = {
  your_app = {
    bucket_name   = "your-unique-bucket-name"
    host_record   = "cdn"  # 将创建 cdn.cdn-ext.your-domain.com
    styles        = ["logoq70", "logo200rd"]
    cdn_auth_type = "type_a"
    cdn_auth_key  = "your-auth-key"
  }
}
```

### 3. 部署

```bash
# 初始化 Terraform
terraform init

# 查看执行计划
terraform plan

# 应用配置
terraform apply
```

## 配置说明

### domain_list 变量结构

```hcl
domain_list = {
  logical_name = {
    bucket_name   = string        # OSS 存储桶名称 (必须全局唯一)
    host_record   = string        # 子域名前缀
    styles        = list(string)  # 图片处理样式列表 (可选)
    cdn_auth_type = string        # CDN 认证类型 (可选，默认: type_a)
    cdn_auth_key  = string        # CDN 认证密钥 (可选)
  }
}
```

### 预定义的图片处理样式

| 样式名称       | 处理规则                                                       | 用途            |
| -------------- | -------------------------------------------------------------- | --------------- |
| `logoq70`      | `image/auto-orient,1/quality,q_70`                             | Logo 压缩       |
| `logo200rd`    | `image/resize,m_lfit,w_200,limit_0/auto-orient,0/quality,q_90` | Logo 200px 宽度 |
| `logo300rd`    | `image/resize,m_lfit,w_300,limit_0/auto-orient,0/quality,q_90` | Logo 300px 宽度 |
| `photo200w`    | `image/resize,m_lfit,w_200,limit_0/auto-orient,0/quality,q_90` | 照片 200px 宽度 |
| `logo100w100h` | `image/resize,m_lfit,w_100,limit_0/auto-orient,0/quality,q_90` | Logo 100px 宽度 |

## 输出信息

部署完成后，Terraform 将输出以下信息：

- `oss_buckets`: OSS 存储桶信息
- `cdn_domains`: CDN 域名信息
- `dns_records`: DNS 记录信息
- `bucket_styles`: 存储桶样式信息
- `summary`: 资源创建汇总

## 使用示例

### 添加新域名

在 `terraform.tfvars` 中添加新的域名配置：

```hcl
domain_list = {
  # 现有配置...

  # 新增域名
  new_domain = {
    bucket_name   = "my-new-bucket"
    host_record   = "new"
    styles        = ["logoq70", "photo200w"]
    cdn_auth_type = "type_a"
    cdn_auth_key  = "my-auth-key"
  }
}
```

然后运行 `terraform apply` 即可创建新的资源。

### 自定义图片处理样式

如需添加自定义样式，在 `terraform.tfvars` 中定义：

```hcl
style_definitions = {
  # 保留默认样式...
  logoq70 = "image/auto-orient,1/quality,q_70"

  # 添加自定义样式
  custom_thumb = "image/resize,m_lfit,w_150,h_150,limit_0/auto-orient,0/quality,q_85"
}
```

## 注意事项

1. **存储桶名称**: OSS 存储桶名称必须全局唯一
2. **SSL 证书**: 确保 SSL 证书在指定区域可用
3. **DNS 权限**: 确保有域名的 DNS 管理权限
4. **资源组**: 确保指定的资源组存在且有权限
5. **成本控制**: CDN 和 OSS 会产生费用，请注意成本控制

## 故障排除

### 常见问题

1. **存储桶名称冲突**: 修改 `bucket_name` 为唯一值
2. **证书不存在**: 检查 `certificate_name` 和 `certificate_region`
3. **权限不足**: 确保 AccessKey 有足够的权限

### 清理资源

```bash
# 销毁所有创建的资源
terraform destroy
```

## 项目状态

✅ **已完成功能**:

- 多域名批量创建
- OSS 存储桶和样式配置
- CDN 域名和配置
- DNS 记录自动创建
- 完整的输出信息

🚧 **计划功能**:

- 支持更多 CDN 配置选项
- 添加监控和告警配置
- 支持多区域部署

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个配置。
