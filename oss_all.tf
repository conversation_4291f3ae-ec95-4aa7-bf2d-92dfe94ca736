# main.tf
variable "region" {
  default = "cn-shenzhen"
}

variable "resource_group_id" {
  default = "rg-aek2chcm3noot3a"
}

provider "alicloud" {
  region = var.region
}

# -----------------------------------------------------
# 核心配置：定义所有要创建的域名信息
# 这是一个"map(object(...))"类型的变量。
# map的键 (如 "images_cdn", "assets_cdn") 是每个配置的逻辑名称，用于在Terraform内部引用。
# map的值是一个对象，包含每个CDN设置所需的具体信息。
# -----------------------------------------------------

variable "domain_list" {
  type = map(object({
    host_record   = string
    bucket_name   = string
    styles        = list(string)
    cdn_auth_type = string
    cdn_auth_key  = string
  }))
  default = {
    hmextarbiter = {
      bucket_name   = "hmextarbiter"
      cdn_domain   = "arb.cdn-ext.seedinglove.com"
      host_record = "arb"
      
    }
    hmextcool = {
      bucket_name   = "hmextcool"
      cdn_domain   = "col.cdn-ext.seedinglove.com"
      host_record = "col"
    }
  }
}

# --- 域名及cdn ---
# 域名(改为您的域名)
variable "domain_name" {
  type        = string
  default     = "seedinglove.com"
  description = "your domain name"
}

# 主机记录
variable "host_record" {
  type        = string
  default     = "arb"
  description = "Host Record,like image,"
}

# 添加一个加速域名
resource "alicloud_cdn_domain_new" "default" {
  domain_name       = format("%s.cdn-ext.%s", var.host_record, var.domain_name)
  resource_group_id = alicloud_resource_manager_resource_group.default.id
  cdn_type          = "web"
  
  sources {
    port     = 443
    weight   = 10
    priority = 20
    type     = "oss"
    content  = format("%s.%s", alicloud_oss_bucket.example.bucket, alicloud_oss_bucket.example.extranet_endpoint)
  }
  
  certificate_config {
    cert_name          = "test.cdn.jobxjoy.cn-250805"
    cert_type          = "cas"
    cert_region        = "cn-shenzhen"
  }
}
# 配置 cdn auth
resource "alicloud_cdn_domain_config" "auth_config" {
  domain_name   = alicloud_cdn_domain_new.default.domain_name
  function_name = "aliauth"
  
  function_args {
    arg_value = "type_a"
    arg_name  = "auth_type"
  }
  function_args {
    arg_value = "hmcdn8dfd"
    arg_name  = "auth_key1"
  }
  function_args {
    arg_value = "hmcdn8dfd"
    arg_name  = "auth_key2"
  }
  function_args {
    arg_value = "1800"
    arg_name  = "ali_auth_delta"
  }
  
}
# 配置 cdn 性能调优
resource "alicloud_cdn_domain_config" "performance_config" {
  domain_name   = alicloud_cdn_domain_new.default.domain_name
  function_name = "set_hashkey_args"
  
  function_args {
    arg_value = "hashkey_args"
    arg_name  = "x-oss-process,rand,r"
  }
  function_args {
    arg_value = "on"
    arg_name  = "disable"
  }
  function_args {
    arg_value = "on"
    arg_name  = "keep_oss_args"
  }
  
}
# 配置 cdn 回源设置
resource "alicloud_cdn_domain_config" "default" {
  domain_name   = alicloud_cdn_domain_new.default.domain_name
  function_name = "l2_oss_key"
  
  function_args {
    arg_value = "on"
    arg_name  = "private_oss_auth"
  }
  
}

# 域名解析
resource "alicloud_dns_record" "example" {
  name        = var.domain_name
  type        = "CNAME"
  host_record = var.host_record
  value       = alicloud_cdn_domain_new.default.cname
  ttl         = 600
}
# 创建存储空间
resource "alicloud_oss_bucket" "example" {
  bucket = "jobxjoy-test"
  storage_class                            = "Standard"
  redundancy_type                          = "LRS"
  resource_group_id                        =  var.resource_group_id
}

# --- oss 及 策略 ---
# 创建 oss 样式策略
resource "alicloud_oss_bucket_style" "logoq70" {
  bucket     = alicloud_oss_bucket.example.bucket
  content    = "image/auto-orient,1/quality,q_70"
  style_name = "logoq70"
}

resource "alicloud_oss_bucket_style" "logo200rd" {
  bucket     = alicloud_oss_bucket.example.bucket
  content    = "image/resize,m_lfit,w_200,limit_0/auto-orient,0/quality,q_90"
  style_name = "logo200rd"
}

resource "alicloud_oss_bucket_style" "logo300rd" {
  bucket     = alicloud_oss_bucket.example.bucket
  content    = "image/resize,m_lfit,w_300,limit_0/auto-orient,0/quality,q_90"
  style_name = "logo300rd"
}

resource "alicloud_oss_bucket_style" "photo200w" {
  bucket     = alicloud_oss_bucket.example.bucket
  content    = "image/resize,m_lfit,w_200,limit_0/auto-orient,0/quality,q_90"
  style_name = "photo200w"
}

resource "alicloud_oss_bucket_style" "logo100w100h" {
  bucket     = alicloud_oss_bucket.example.bucket
  content    = "image/resize,m_lfit,w_100,limit_0/auto-orient,0/quality,q_90"
  style_name = "logo100w100h"
}