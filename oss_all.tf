# =============================================================================
# Terraform Configuration for OSS + CDN Multi-Domain Setup
# =============================================================================

# -----------------------------------------------------------------------------
# Provider Configuration
# -----------------------------------------------------------------------------
terraform {
  required_providers {
    alicloud = {
      source  = "aliyun/alicloud"
      version = "~> 1.0"
    }
  }
}

provider "alicloud" {
  region = var.region
}

# -----------------------------------------------------------------------------
# Variables
# -----------------------------------------------------------------------------
variable "region" {
  type        = string
  default     = "cn-shenzhen"
  description = "Alibaba Cloud region"
}

variable "resource_group_id" {
  type        = string
  default     = "rg-aek2chcm3noot3a"
  description = "Resource group ID"
}

variable "domain_name" {
  type        = string
  default     = "seedinglove.com"
  description = "Base domain name"
}

variable "certificate_name" {
  type        = string
  default     = "_.cdn-ext.seedinglove.com"
  description = "SSL certificate name"
}

variable "certificate_region" {
  type        = string
  default     = "cn-shenzhen"
  description = "SSL certificate region"
}

# -----------------------------------------------------------------------------
# Domain Configuration
# 核心配置：定义所有要创建的域名信息
# map的键是每个配置的逻辑名称，用于在Terraform内部引用
# map的值包含每个CDN设置所需的具体信息
# -----------------------------------------------------------------------------
variable "domain_list" {
  type = map(object({
    host_record   = string
    bucket_name   = string
    cdn_auth_type = optional(string, "type_a")
    cdn_auth_key  = optional(string, "hmcdn8dfd")
  }))
  default = {
    hmextarbiter = {
      bucket_name   = "hmextarbiter"
      host_record   = "arb"
      cdn_auth_type = "type_a"
      cdn_auth_key  = "hmcdn8dfd"
    }
    hmextcool = {
      bucket_name   = "hmextcool"
      host_record   = "col"
      cdn_auth_type = "type_a"
      cdn_auth_key  = "hmcdn8dfd"
    }
    hmextgreen = {
      bucket_name   = "hmextgreen"
      host_record   = "grn"
      cdn_auth_type = "type_a"
      cdn_auth_key  = "hmcdn8dfd"
    }
  }
  description = "Configuration for each domain including bucket, host record, and CDN auth settings. All buckets will use all available styles."
}


# -----------------------------------------------------------------------------
# OSS Buckets
# -----------------------------------------------------------------------------
resource "alicloud_oss_bucket" "buckets" {
  for_each = var.domain_list

  bucket                = each.value.bucket_name
  storage_class         = "Standard"
  redundancy_type       = "LRS"
  resource_group_id     = var.resource_group_id
}

# -----------------------------------------------------------------------------
# OSS Bucket Styles
# -----------------------------------------------------------------------------
# 创建 oss 样式策略
resource "alicloud_oss_bucket_style" "logoq70" {
  for_each = var.domain_list
  bucket     = alicloud_oss_bucket.buckets[each.key].bucket
  content    = "image/auto-orient,1/quality,q_70"
  style_name = "logoq70"
  depends_on = [alicloud_oss_bucket.buckets]
}

resource "alicloud_oss_bucket_style" "logo200rd" {
  for_each = var.domain_list
  bucket     = alicloud_oss_bucket.buckets[each.key].bucket
  content    = "image/resize,m_lfit,w_200,limit_0/auto-orient,0/quality,q_90"
  style_name = "logo200rd"
  depends_on = [alicloud_oss_bucket.buckets]
}

resource "alicloud_oss_bucket_style" "logo300rd" {
  for_each = var.domain_list
  bucket     = alicloud_oss_bucket.buckets[each.key].bucket
  content    = "image/resize,m_lfit,w_300,limit_0/auto-orient,0/quality,q_90"
  style_name = "logo300rd"
  depends_on = [alicloud_oss_bucket.buckets]
}

resource "alicloud_oss_bucket_style" "photo200w" {
  for_each = var.domain_list
  bucket     = alicloud_oss_bucket.buckets[each.key].bucket
  content    = "image/resize,m_lfit,w_200,limit_0/auto-orient,0/quality,q_90"
  style_name = "photo200w"
  depends_on = [alicloud_oss_bucket.buckets]
}

resource "alicloud_oss_bucket_style" "logo100w100h" {
  for_each = var.domain_list
  bucket     = alicloud_oss_bucket.buckets[each.key].bucket
  content    = "image/resize,m_lfit,w_100,limit_0/auto-orient,0/quality,q_90"
  style_name = "logo100w100h"
  depends_on = [alicloud_oss_bucket.buckets]
}


# -----------------------------------------------------------------------------
# CDN Domains
# -----------------------------------------------------------------------------
resource "alicloud_cdn_domain_new" "domains" {
  for_each = var.domain_list

  domain_name       = format("%s.cdn-ext.%s", each.value.host_record, var.domain_name)
  resource_group_id = var.resource_group_id
  cdn_type          = "web"

  sources {
    port     = 443
    weight   = 10
    priority = 20
    type     = "oss"
    content  = format("%s.%s",
      alicloud_oss_bucket.buckets[each.key].bucket,
      alicloud_oss_bucket.buckets[each.key].extranet_endpoint
    )
  }

  certificate_config {
    cert_name   = var.certificate_name
    cert_type   = "cas"
    cert_region = var.certificate_region
  }
  depends_on = [alicloud_oss_bucket.buckets]
}

# -----------------------------------------------------------------------------
# CDN Domain Configurations
# -----------------------------------------------------------------------------

# CDN Authentication Configuration
resource "alicloud_cdn_domain_config" "auth_config" {
  for_each = var.domain_list

  domain_name   = alicloud_cdn_domain_new.domains[each.key].domain_name
  function_name = "aliauth"

  function_args {
    arg_value = each.value.cdn_auth_type
    arg_name  = "auth_type"
  }
  function_args {
    arg_value = each.value.cdn_auth_key
    arg_name  = "auth_key1"
  }
  function_args {
    arg_value = each.value.cdn_auth_key
    arg_name  = "auth_key2"
  }
  function_args {
    arg_value = "1800"
    arg_name  = "ali_auth_delta"
  }

  depends_on = [alicloud_cdn_domain_new.domains]
}

# CDN 性能调优
resource "alicloud_cdn_domain_config" "performance_config" {
  for_each = var.domain_list

  domain_name   = alicloud_cdn_domain_new.domains[each.key].domain_name
  function_name = "set_hashkey_args"

  function_args {
    arg_value = "x-oss-process,rand,r"
    arg_name  = "hashkey_args"
  }
  function_args {
    arg_value = "on"
    arg_name  = "disable"
  }
  function_args {
    arg_value = "on"
    arg_name  = "keep_oss_args"
  }

  depends_on = [alicloud_cdn_domain_new.domains]
}

# 配置 cdn 回源设置
resource "alicloud_cdn_domain_config" "origin_config" {
  for_each = var.domain_list

  domain_name   = alicloud_cdn_domain_new.domains[each.key].domain_name
  function_name = "l2_oss_key"

  function_args {
    arg_value = "on"
    arg_name  = "private_oss_auth"
  }

  depends_on = [alicloud_cdn_domain_new.domains]
}

# -----------------------------------------------------------------------------
# DNS 域名解析
# -----------------------------------------------------------------------------
resource "alicloud_dns_record" "cdn_records" {
  for_each = var.domain_list

  name        = var.domain_name
  type        = "CNAME"
  host_record = each.value.host_record
  value       = alicloud_cdn_domain_new.domains[each.key].cname
  ttl         = 600

  depends_on = [alicloud_cdn_domain_new.domains]
}

# -----------------------------------------------------------------------------
# Outputs
# -----------------------------------------------------------------------------
output "oss_buckets" {
  description = "Information about created OSS buckets"
  value = {
    for k, v in alicloud_oss_bucket.buckets : k => {
      bucket_name        = v.bucket
      bucket_domain_name = v.bucket_domain_name
      extranet_endpoint  = v.extranet_endpoint
      intranet_endpoint  = v.intranet_endpoint
    }
  }
}

output "cdn_domains" {
  description = "Information about created CDN domains"
  value = {
    for k, v in alicloud_cdn_domain_new.domains : k => {
      domain_name = v.domain_name
      cname       = v.cname
      status      = v.status
    }
  }
}

output "dns_records" {
  description = "Information about created DNS records"
  value = {
    for k, v in alicloud_dns_record.cdn_records : k => {
      host_record = v.host_record
      value       = v.value
      type        = v.type
    }
  }
}

output "bucket_styles" {
  description = "Information about created bucket styles"
  value = {
    for k, v in alicloud_oss_bucket_style.styles : k => {
      bucket     = v.bucket
      style_name = v.style_name
      content    = v.content
    }
  }
}

# Summary output for easy reference
output "summary" {
  description = "Summary of all created resources"
  value = {
    total_buckets     = length(var.domain_list)
    total_cdn_domains = length(var.domain_list)
    total_dns_records = length(var.domain_list)
    total_styles      = length(local.bucket_styles_map)

    domain_mappings = {
      for k, v in var.domain_list : k => {
        bucket_name = v.bucket_name
        host_record = v.host_record
        cdn_domain  = format("%s.cdn-ext.%s", v.host_record, var.domain_name)
      }
    }

    # 所有桶都使用的样式列表
    available_styles = keys(var.style_definitions)
  }
}