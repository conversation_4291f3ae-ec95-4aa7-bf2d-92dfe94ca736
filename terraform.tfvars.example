# =============================================================================
# Terraform Variables Configuration Example
# =============================================================================
# Copy this file to terraform.tfvars and modify the values according to your needs

# -----------------------------------------------------------------------------
# Basic Configuration
# -----------------------------------------------------------------------------
region            = "cn-shenzhen"
resource_group_id = "rg-aek2chcm3noot3a"
domain_name       = "seedinglove.com"

# SSL Certificate Configuration
certificate_name   = "test.cdn.jobxjoy.cn-250805"
certificate_region = "cn-shenzhen"

# -----------------------------------------------------------------------------
# Domain List Configuration
# 每个域名配置包含以下字段：
# - host_record: 子域名前缀 (例如: "arb" 将创建 "arb.cdn-ext.seedinglove.com")
# - bucket_name: OSS存储桶名称 (必须全局唯一)
# - cdn_auth_type: CDN认证类型 (可选，默认为 "type_a")
# - cdn_auth_key: CDN认证密钥 (可选，默认为 "hmcdn8dfd")
# 注意：所有存储桶都会自动创建全部的图片处理样式
# -----------------------------------------------------------------------------
domain_list = {
  # 仲裁者域名配置
  hmextarbiter = {
    bucket_name   = "hmextarbiter"
    host_record   = "arb"
    cdn_auth_type = "type_a"
    cdn_auth_key  = "hmcdn8dfd"
  }

  # 酷炫域名配置
  hmextcool = {
    bucket_name   = "hmextcool"
    host_record   = "col"
    cdn_auth_type = "type_a"
    cdn_auth_key  = "hmcdn8dfd"
  }

  # 绿色域名配置
  hmextgreen = {
    bucket_name   = "hmextgreen"
    host_record   = "grn"
    cdn_auth_type = "type_a"
    cdn_auth_key  = "hmcdn8dfd"
  }

  # 可以添加更多域名配置
  # hmextblue = {
  #   bucket_name   = "hmextblue"
  #   host_record   = "blu"
  #   cdn_auth_type = "type_a"
  #   cdn_auth_key  = "hmcdn8dfd"
  # }
}

# -----------------------------------------------------------------------------
# 可选：自定义样式定义
# 如果需要修改默认的图片处理样式，可以取消注释并修改以下配置
# -----------------------------------------------------------------------------
# style_definitions = {
#   logoq70      = "image/auto-orient,1/quality,q_70"
#   logo200rd    = "image/resize,m_lfit,w_200,limit_0/auto-orient,0/quality,q_90"
#   logo300rd    = "image/resize,m_lfit,w_300,limit_0/auto-orient,0/quality,q_90"
#   photo200w    = "image/resize,m_lfit,w_200,limit_0/auto-orient,0/quality,q_90"
#   logo100w100h = "image/resize,m_lfit,w_100,limit_0/auto-orient,0/quality,q_90"
#   # 可以添加自定义样式
#   # custom_style = "image/resize,m_lfit,w_500,limit_0/auto-orient,0/quality,q_80"
# }
